<script setup lang="ts">
  import type { ListItm } from '../../index.d.ts';

  import { nextTick, onMounted, onUnmounted, reactive, ref, watch } from 'vue';

  import { useVbenModal } from '@vben/common-ui';

  import { MinusOutlined, PlusOutlined } from '@ant-design/icons-vue';

  import {
    useAbstractData,
    useAccountSubjects,
    useSubjectData,
  } from '#/hooks/account-book/voucher/index';
  import { useCurrentCustomerStore } from '#/store/account-book/company';
  import { uTconvert, uTcurrencyToUpperCase } from '#/utils/index';

  import AuxiliarySelect from '../../components/AuxiliarySelect/index.vue';
  import UserSelectBox from '../components/UserSelectBox.vue';
  import { validateVoucherData } from '../utils/validation';
  import AddSubjectPop from './AddSubjectPop.vue';
  import AddSummaryPop from './AddSummaryPop.vue';
  import Ulvalue from './Ulvalue.vue';
  import emitter from './usermitt';

  const useCustomer = useCurrentCustomerStore();
  // const emits = defineEmits(['changeSelectValue', 'changeInputValue', 'addOrDeleteTable', 'foreignChange']);
  let oldvoucherdata: null | string = localStorage.getItem(
    `${import.meta.env.VITE_APP_NAMESPACE}-current-voucher`,
  );
  const oldvoucherdata_customer_id: null | string = localStorage.getItem(
    `${import.meta.env.VITE_APP_NAMESPACE}-current-voucher-customer_id`,
  );
  // 此处需要对公司进行判断如果是切换公司了 这里的存储要清空下
  // useCustomer.customerId
  if (oldvoucherdata) {
    if (oldvoucherdata_customer_id == useCustomer.customerId) {
      oldvoucherdata = JSON.parse(oldvoucherdata as string);
    } else {
      oldvoucherdata = null;
      localStorage.setItem(
        `${import.meta.env.VITE_APP_NAMESPACE}-current-voucher`,
        '',
      );
    }
  }
  // const { borrowerAll, lenderAll, list, isCurrency, amountTo } = defineProps<{
  //     amountTo: string;
  //     borrowerAll: string;
  //     isCurrency: boolean;
  //     lenderAll: string;
  //     list: ListItm[];
  // }>();

  const state = reactive<{
    amountTo: string;
    borrowerAll: string;
    isCurrency: boolean;
    lenderAll: string;
    list: ListItm[];
  }>({
    amountTo: '',
    borrowerAll: '',
    isCurrency: false, // 是否有外币这一列
    lenderAll: '',
    list: oldvoucherdata || [
      // 默认有四组数据
      {
        balance: 0,
        borrower: '',
        lender: '',
      },
      {
        balance: 0,
        borrower: '',
        lender: '',
      },
      {
        balance: 0,
        borrower: '',
        lender: '',
      },
      {
        balance: 0,
        borrower: '',
        lender: '',
      },
    ],
  });

  const [SummaryModal, SummarymodalApi] = useVbenModal({
    // 连接抽离的组件
    connectedComponent: AddSummaryPop,
  });
  const [SubjectModal, SubjectmodalApi] = useVbenModal({
    // 连接抽离的组件
    connectedComponent: AddSubjectPop,
  });
  const outDomRef = ref<any>(null);
  type TargetXY = {
    h: number;
    w: number;
    x: number;
    y: number;
  };
  // 摘要
  const abstractCoor = ref<TargetXY | undefined>(); // 摘要选择位置坐标
  const subjectCoor = ref<TargetXY | undefined>(); // 科目选择位置坐标

  const currencyCoor = ref<TargetXY | undefined>(); // 汇率输入位置坐标
  const foreignMoney = ref<string>(''); // 外币金额
  const foreignRate = ref<string>(''); // 外币汇率
  const foreignType = ref<string>(''); // 外币种类
  const inputValue = ref<string>('');
  const inputRef = ref<any>();
  const inlineInputRef = ref<any>();

  // 内联编辑状态管理
  const editingCell = ref<null | {
    index: number;
    type: 'borrower' | 'lender';
  }>(null);
  // 摘要数据
  const useAbstract = useAbstractData();
  const useSubject = useSubjectData();

  // 新的会计科目选择器（带缓存）
  const {
    clearCache: clearSubjectCache,
    error: subjectError,
    loading: subjectLoading,
    refreshData: refreshSubjectData,
    subjectOptions,
  } = useAccountSubjects();
  const selectshow = (e?: Event) => {
    // 如果点击的是内联编辑输入框，不清除编辑状态
    if (e && e.target) {
      const target = e.target as HTMLElement;
      console.log('selectshow 点击目标:', target.className, target.tagName);

      if (
        target.classList.contains('inline-amount-input') ||
        target.closest('.inline-amount-input') ||
        target.classList.contains('amount-display') ||
        target.closest('.amount-display') ||
        target.closest('.amount-cell')
      ) {
        console.log('跳过清除编辑状态');
        return;
      }
    }

    console.log('清除编辑状态');
    abstractCoor.value = undefined;
    subjectCoor.value = undefined;
    currencyCoor.value = undefined;
    editingCell.value = null; // 清除编辑状态
  };
  let currtIndex = 0; // 当前输入行
  const inputType = ''; // input输入框的类型 用来区分是借方还是贷方

  // 数据验证
  const data_validation = (type?: string) => {
    return validateVoucherData(
      state.list,
      type,
      state.borrowerAll,
      state.lenderAll,
    );
  };

  // 这是新增摘要保存成功的时候列表要显示上去 所以此处模拟触发下change事件
  const listenmitt = (data: any) => {
    selectChange(data, 'abstract');
    // emits('changeSelectValue', data, 'abstract', currtIndex);
  };
  // 新增的科目通知到这里直接填写上去
  const listenSubjectMitt = (data: any) => {
    selectChange(data, 'subject');
  };
  const set_quick_edit = (data: any) => {
    console.log('shun22', data);
    // 第一步先清空数据
    clearTableData();
    // 赋值 第一行
    if (data.debitSubjectData) {
      currtIndex = 0;
      selectChange({ text: data.serverType.summary }, 'abstract'); // 设置摘要
      selectChange(data.debitSubjectData, 'subject'); // 设置科目
    }
    if (data.creditSubjectData) {
      currtIndex = 1;
      selectChange({ text: data.serverType.summary }, 'abstract'); // 设置摘要
      selectChange(data.creditSubjectData, 'subject'); // 设置科目
    }
    if (data.taxSubjectData) {
      currtIndex = 2;
      selectChange({ text: data.serverType.summary }, 'abstract'); // 设置摘要
      selectChange(data.taxSubjectData, 'subject'); // 设置科目
    }
    // debitSubjectData
  };
  // 监听localStorage变化，重新加载数据
  let lastVoucherDataHash = '';
  let hasInitialized = false;

  const checkAndUpdateVoucherData = () => {
    const currentVoucherData = localStorage.getItem(
      `${import.meta.env.VITE_APP_NAMESPACE}-current-voucher`,
    );
    const currentCustomerId = localStorage.getItem(
      `${import.meta.env.VITE_APP_NAMESPACE}-current-voucher-customer_id`,
    );

    // 检查数据是否有变化
    const currentDataHash = currentVoucherData || '';

    // 如果是第一次检查，或者数据确实有变化，或者当前state.list是空的默认数据，就进行更新
    const isDefaultData =
      state.list.length === 4 &&
      state.list.every(
        (item) =>
          !item.borrower && !item.lender && !item.abstract && !item.subject,
      );

    if (
      hasInitialized &&
      currentDataHash === lastVoucherDataHash &&
      !isDefaultData
    ) {
      return; // 数据没有变化，不需要更新
    }

    if (currentVoucherData) {
      try {
        const parsedData = JSON.parse(currentVoucherData);

        // 强制更新，确保合计能正确计算
        state.list.splice(0, state.list.length, ...parsedData);

        // 手动计算合计，确保显示正确
        let borrower = 0;
        let lender = 0;

        state.list.forEach((item: any) => {
          const borrowerNum = Number(item.borrower || 0);
          const lenderNum = Number(item.lender || 0);
          borrower += borrowerNum;
          lender += lenderNum;
        });

        // 直接设置state值
        state.borrowerAll = `${borrower}`;
        state.lenderAll = `${lender}`;

        state.amountTo =
          borrower === lender && borrower !== 0
            ? uTcurrencyToUpperCase(borrower)
            : '';

        lastVoucherDataHash = currentDataHash;
        hasInitialized = true;
      } catch (error) {
        console.error('VoucherEditing - 解析localStorage数据失败:', error);
      }
    }
  };

  onMounted(() => {
    document.addEventListener('click', selectshow);
    emitter.on('account_voucher_newly_added', listenmitt);
    emitter.on('account_voucher_subject_added', listenSubjectMitt);
    emitter.on('quick_edit_set_voucher', set_quick_edit);

    // 定期检查localStorage变化
    const intervalId = setInterval(checkAndUpdateVoucherData, 500);

    // 组件卸载时清除定时器
    onUnmounted(() => {
      clearInterval(intervalId);
    });
  });
  onUnmounted(() => {
    document.removeEventListener('click', selectshow);
    emitter.off('account_voucher_newly_added', listenmitt);
    emitter.off('account_voucher_subject_added', listenSubjectMitt);
    emitter.off('quick_edit_set_voucher', set_quick_edit);
  });
  const getTargetXY = (DOM: any, index: number) => {
    // 使用 getBoundingClientRect 获取相对于视口的坐标
    const rect = DOM.getBoundingClientRect();
    const selectBoxHeight = 300; // 选择框的高度
    const viewportHeight = window.innerHeight;

    // 计算是否有足够空间在下方显示
    const spaceBelow = viewportHeight - rect.bottom;
    const spaceAbove = rect.top;

    // 如果下方空间不足且上方空间更大，则向上显示
    let y = rect.bottom;
    if (spaceBelow < selectBoxHeight && spaceAbove > spaceBelow) {
      y = rect.top - selectBoxHeight;
    }

    return {
      h: rect.height,
      w: rect.width,
      x: rect.left,
      y: Math.max(10, y), // 确保不会超出视口顶部，至少距离顶部10px
    };
  };

  const getAppointDom = (dom: any, classname: string) => {
    let nodeDom = dom;
    let index = 0;
    while (!nodeDom.className?.includes(classname) && index < 30) {
      nodeDom = nodeDom.parentNode;
      index++;
    }
    if (index === 30) {
      console.error('调用getAppointDom 出错没有找到元素');
    }
    return nodeDom;
  };
  /**
   * 选项区域点击事件
   */
  const selectAreClick = (e: Event, type: string, index: number) => {
    currtIndex = index;
    e.preventDefault();
    e.stopPropagation();
    if (type === 'abstract') {
      const data = getTargetXY(e.target, index);
      abstractCoor.value = data;
      subjectCoor.value = undefined;
      currencyCoor.value = undefined;
      editingCell.value = null; // 清除编辑状态
      // 获取摘要数据
      if (useAbstract.selectdata.value.length === 0) {
        // 获取数据
        useAbstract.fetchData();
      }
    } else if (type === 'subject') {
      const Dom = getAppointDom(e.target, 'subjectcont');
      const data = getTargetXY(Dom, index);
      abstractCoor.value = undefined;
      currencyCoor.value = undefined;
      editingCell.value = null; // 清除编辑状态
      subjectCoor.value = data;

      // 获取科目数据 - 使用新的 hooks（优先）
      if (subjectOptions.value.length === 0 && !subjectLoading.value) {
        // 新的 hooks 会自动获取数据，这里只需要检查是否有错误
        if (subjectError.value) {
          console.warn(
            '新科目选择器加载失败，回退到旧版本:',
            subjectError.value,
          );
          // 回退到旧版本
          if (useSubject.selectdata.value.length === 0) {
            useSubject.fetchData();
          }
        }
      } else if (useSubject.selectdata.value.length === 0) {
        // 旧版本兜底
        useSubject.fetchData();
      }
    }
  };
  // 金额输入区域点击事件 - 使用内联编辑
  const inputAreClick = (
    e: any,
    type: 'borrower' | 'lender',
    index: number,
  ) => {
    e.preventDefault();
    e.stopPropagation();

    // 清除其他编辑状态
    abstractCoor.value = undefined;
    subjectCoor.value = undefined;
    currencyCoor.value = undefined;

    // 设置当前编辑的单元格
    editingCell.value = { index, type };
    console.log('设置编辑状态:', editingCell.value);

    // 设置输入框的初始值
    if (type === 'borrower') {
      inputValue.value = state.list[index]?.borrower as string;
    } else if (type === 'lender') {
      inputValue.value = state.list[index]?.lender as string;
    }

    // 下一帧聚焦并选中输入框内容
    nextTick(() => {
      // 等待DOM更新后再设置焦点
      setTimeout(() => {
        const inputElement = document.querySelector(
          '.inline-amount-input',
        ) as HTMLInputElement;
        if (inputElement) {
          inputElement.focus();
          inputElement.select();
        }
      }, 10);
    });
  };
  // 外币输入区域点击事件
  const foreignInputClick = (e: any, index: number) => {
    e.preventDefault();
    e.stopPropagation();
    currtIndex = index;
    const data = getTargetXY(e.target, index);
    currencyCoor.value = data;
    abstractCoor.value = undefined;
    subjectCoor.value = undefined;
    editingCell.value = null; // 清除编辑状态
    foreignMoney.value = state.list[index]?.currency?.money as string; // 外币金额
    foreignRate.value = state.list[index]?.currency?.rate as string; // 外币汇率
    foreignType.value = state.list[index]?.currency?.currencyCode as string; // 外币种类
  };
  // 内联编辑失焦事件
  const handleInlineInputBlur = () => {
    if (!editingCell.value) return;

    const { index, type } = editingCell.value;
    const itm = state.list[index];

    if (itm) {
      if (type === 'borrower' && itm.borrower !== inputValue.value) {
        // 借方
        itm.borrower = inputValue.value;
        itm.lender = '';
        // 进行余额计算
        if (itm.subject) {
          itm.balance = Number(itm.subject.balance) + Number(itm.borrower);
        }
      } else if (type === 'lender' && itm.lender !== inputValue.value) {
        // 贷方
        itm.lender = inputValue.value;
        itm.borrower = '';
        if (itm.subject) {
          itm.balance = itm.subject.balance - Number(itm.lender);
        }
      }
      // TODO 当有外币的时候计算比较特殊
    }

    // 清除编辑状态
    editingCell.value = null;

    // 重新计算合计
    processingDataFilled(state.list);
  };

  // 保留原有的 inputblur 函数以兼容其他地方的调用
  const inputblur = () => {
    handleInlineInputBlur();
  };
  const selectChange = (data: any, type: string) => {
    const itm = state.list[currtIndex];
    if (itm) {
      if (type === 'abstract') {
        // 摘要设置
        itm.abstract = data;
      } else if (type === 'subject') {
        // 处理新版科目选择器的数据格式
        if (data.value && data.label) {
          // 新版科目选择器数据格式
          const subjectData = {
            assistantOptions: data.assistantOptions,
            assistantType: data.assistantType,
            balance: 0, // 默认余额，可能需要从其他地方获取
            code: data.code,
            currencyCode: '',
            id: data.value,
            // 兼容旧版本字段
            isForCurrency: false, // 暂时设为false，后续可以根据需要调整
            name: data.name,
            text: data.label, // 兼容旧版本
            useAssistant: data.useAssistant,
          };

          itm.subject = subjectData;
          itm.balance = subjectData.balance;

          // 处理外币核算
          if (subjectData.isForCurrency) {
            itm.currency = {
              currencyCode: subjectData.currencyCode,
              money: '',
              rate: '',
            };
          } else {
            delete itm.currency;
          }
        } else {
          // 旧版科目选择器数据格式（兼容）
          itm.subject = data;
          itm.balance = data.balance || 0;

          // 判断科目里是否有外币
          if (data.isForCurrency) {
            // 有外币核算
            itm.currency = {
              currencyCode: data.currencyCode,
              money: '',
              rate: '',
            };
          } else {
            delete itm.currency;
          }
        }
      }
    }
    // emits('changeSelectValue', itm, type, currtIndex);
  };
  const handleclick = (type: string, index: number) => {
    if (type === 'add') {
      // 添加 在当前行的下面添加
      state.list.splice(index + 1, 0, {
        balance: 0,
        borrower: '',
        lender: '',
      });
    } else if (type === 'delete') {
      // 删除
      // 如果多余4个直接删除当前行
      // 小于4个是清空当前行的数据
      if (state.list.length > 4) {
        state.list.splice(index, 1);
      } else {
        if (state.list[index]) {
          state.list[index].abstract = undefined;
          state.list[index].subject = undefined;
          state.list[index].borrower = '';
          state.list[index].lender = '';
          delete state.list[index].currency;
        }
      }
    }
    // mits('addOrDeleteTable', type, index);
  };
  // 外币数量输入失去焦点事件
  const foreignMoneyBlur = () => {
    const itm = state.list[currtIndex];
    if (itm && itm.currency) {
      itm.currency.money = foreignMoney.value;
    }
    // emits('foreignChange', 'money', foreignMoney.value, currtIndex);
  };
  // 外币数量输入失去焦点事件
  const foreignMoneyRate = () => {
    const itm = state.list[currtIndex];
    if (itm && itm.currency) {
      itm.currency.rate = foreignRate.value;
    }
    // emits('foreignChange', 'rate', foreignRate.value, currtIndex);
  };
  const foreignInputOutClick = (e: any) => {
    e.preventDefault();
    e.stopPropagation();
  };
  // 新增摘要或者科目
  const newlyAddedClick = (type: string) => {
    console.log(type);
    if (type === 'abstract') {
      SummarymodalApi.open();
    } else if (type === 'subject') {
      SubjectmodalApi.open();
    }
  };
  const processingDataFilled = (newval: any) => {
    // 对借方贷方求和
    let borrower = 0;
    let lender = 0;
    let is_foreign_currency = false;

    newval.map((v: any) => {
      const borrowerNum = Number(v.borrower);
      const lenderNum = Number(v.lender);

      borrower += borrowerNum;
      lender += lenderNum;

      if (v.currency) {
        is_foreign_currency = true;
      }
      return true;
    });

    state.borrowerAll = `${borrower}`;
    state.lenderAll = `${lender}`;

    state.amountTo =
      state.borrowerAll == state.lenderAll && Number(state.borrowerAll) !== 0
        ? uTcurrencyToUpperCase(Number(state.borrowerAll))
        : '';

    state.isCurrency = is_foreign_currency; // 是否有外币

    // 对数据进行本地存储便于编写
    localStorage.setItem(
      `${import.meta.env.VITE_APP_NAMESPACE}-current-voucher`,
      JSON.stringify(newval),
    );
    localStorage.setItem(
      `${import.meta.env.VITE_APP_NAMESPACE}-current-voucher-customer_id`,
      (useCustomer as any).customerId,
    );
  };
  watch(
    state.list,
    (newval) => {
      processingDataFilled(newval);
    },
    { flush: 'post' },
  );
  // 清空表格数据
  const clearTableData = () => {
    state.list.map((v) => {
      v.abstract = undefined;
      v.subject = undefined;
      v.borrower = '';
      v.lender = '';
      delete v.currency;
    });
    state.borrowerAll = '';
    state.lenderAll = '';
    state.amountTo = '';
  };

  // 刷新科目数据（包括清除缓存）
  const refreshSubjectDataWithCache = () => {
    console.log('刷新科目数据...');
    // 清除新版科目选择器的缓存
    if (clearSubjectCache) {
      clearSubjectCache();
    }
    // 刷新新版科目数据
    if (refreshSubjectData) {
      refreshSubjectData();
    }
    // 清除旧版科目数据，强制重新获取
    useSubject.selectdata.value = [];
  };

  // 组件暴露给父组件的方法
  defineExpose({
    clearTableData,
    data_validation,
    refreshSubjectDataWithCache,
    state,
  });
</script>
<template>
  <div class="add-voucher-tablebox" ref="outDomRef">
    <div>
      <ul class="headul">
        <li class="li-abstract">摘要</li>
        <li class="li-subject">会计科目</li>
        <li v-if="state.isCurrency" class="li-currency">外币核算</li>
        <li class="li2">
          <ul class="tit">
            <li>借方金额</li>
          </ul>
          <Ulvalue type="head" classname="moneyul" />
        </li>
        <li class="li2">
          <ul class="tit">
            <li>贷方金额</li>
          </ul>
          <Ulvalue type="head" classname="moneyul" />
        </li>
      </ul>
      <ul v-for="(itm, i) in state.list" :key="i" class="bodyul">
        <div class="connect"></div>
        <div class="add">
          <PlusOutlined
            @click="
              () => {
                handleclick('add', i);
              }
            "
          />
        </div>
        <li
          @click="
            (e) => {
              selectAreClick(e, 'abstract', i);
            }
          "
          class="li-abstract"
        >
          <div class="d1 ml-2">
            {{ itm.abstract?.text }}
          </div>
        </li>
        <li
          @click="
            (e) => {
              selectAreClick(e, 'subject', i);
            }
          "
          class="li-subject subjectcont"
        >
          <div v-if="itm.subject" class="subjectbox ml-2">
            <div>{{ itm.subject?.text }}</div>
            <template v-if="itm.subject.types">
              <div
                @click.stop
                class="supplier"
                v-for="(vv, ii) in itm.subject.types"
                :key="ii"
              >
                <div>{{ vv.text }}：</div>
                <div>
                  <AuxiliarySelect :type="vv.type" />
                </div>
              </div>
            </template>

            <div :class="[itm.balance < 0 ? 'text-red-500' : '']">
              余额：{{ itm.balance }}
            </div>
          </div>
        </li>
        <li v-if="state.isCurrency" class="li-currency">
          <div
            class="currencybox"
            v-if="itm.currency"
            @click="
              (e) => {
                foreignInputClick(e, i);
              }
            "
          >
            <div>{{ itm.currency.currencyCode }}：{{ itm.currency.money }}</div>
            <div>汇率：{{ itm.currency.rate }}</div>
          </div>
        </li>
        <!-- 借方金额 -->
        <li class="li2 amount-cell">
          <template
            v-if="
              editingCell &&
              editingCell.index === i &&
              editingCell.type === 'borrower'
            "
          >
            <a-input
              v-model:value="inputValue"
              class="inline-amount-input"
              type="number"
              placeholder="0.00"
              @blur="handleInlineInputBlur"
              @keyup.enter="handleInlineInputBlur"
              @click.stop
            />
          </template>
          <template v-else>
            <div
              class="amount-display"
              @click="(e) => inputAreClick(e, 'borrower', i)"
            >
              <Ulvalue
                classname="moneyul"
                :value="uTconvert(itm.borrower, '元')"
                :original-value="itm.borrower"
              />
            </div>
          </template>
        </li>
        <!-- 贷方金额 -->
        <li class="li2 amount-cell">
          <template
            v-if="
              editingCell &&
              editingCell.index === i &&
              editingCell.type === 'lender'
            "
          >
            <a-input
              v-model:value="inputValue"
              class="inline-amount-input"
              type="number"
              placeholder="0.00"
              @blur="handleInlineInputBlur"
              @keyup.enter="handleInlineInputBlur"
              @click.stop
            />
          </template>
          <template v-else>
            <div
              class="amount-display"
              @click="(e) => inputAreClick(e, 'lender', i)"
            >
              <Ulvalue
                classname="moneyul"
                :value="uTconvert(itm.lender, '元')"
                :original-value="itm.lender"
              />
            </div>
          </template>
        </li>
        <div class="connect-remove"></div>
        <div class="remove">
          <MinusOutlined
            @click="
              () => {
                handleclick('delete', i);
              }
            "
          />
        </div>
      </ul>
      <ul class="footerul">
        <li class="li3" :class="[state.isCurrency ? 'li3-currency' : '']">
          <div>合计：{{ state.amountTo }}</div>
        </li>
        <li class="li2">
          <Ulvalue
            classname="moneyul"
            :value="uTconvert(state.borrowerAll, '元')"
            :original-value="state.borrowerAll"
          />
        </li>
        <li class="li2">
          <Ulvalue
            classname="moneyul"
            :value="uTconvert(state.lenderAll, '元')"
            :original-value="state.lenderAll"
          />
        </li>
      </ul>
      <div
        class="selectbox"
        v-if="abstractCoor"
        :style="{
          width: `${abstractCoor.w}px`,
          left: `${abstractCoor.x}px`,
          top: `${abstractCoor.y}px`,
        }"
      >
        <UserSelectBox
          :options="useAbstract.selectdata"
          type="abstract"
          btntext="新增摘要"
          @select-change="selectChange"
          @newly-added-click="
            () => {
              newlyAddedClick('abstract');
            }
          "
        />
      </div>
      <div
        class="selectbox"
        v-if="subjectCoor"
        :style="{
          width: `${subjectCoor.w}px`,
          left: `${subjectCoor.x}px`,
          top: `${subjectCoor.y}px`,
        }"
      >
        <UserSelectBox
          :options="
            subjectOptions.length > 0 ? subjectOptions : useSubject.selectdata
          "
          type="subject"
          btntext="新增科目"
          @select-change="selectChange"
          @newly-added-click="
            () => {
              newlyAddedClick('subject');
            }
          "
        />
      </div>

      <div
        class="exchange-input"
        v-if="currencyCoor"
        :style="{
          left: `${currencyCoor.x + 100}px`,
          top: `${currencyCoor.y}px`,
        }"
        @click="foreignInputOutClick"
      >
        <div>
          {{ foreignType }}
          <a-input
            style="width: 100px"
            v-model:value="foreignMoney"
            type="number"
            ref="inputRef"
            placeholder=""
            class="text-right"
            @blur="foreignMoneyBlur"
          />
        </div>
        <div class="mt-2">
          汇率
          <a-input
            style="width: 100px"
            v-model:value="foreignRate"
            ref="inputRef"
            placeholder=""
            class="text-right"
            @blur="foreignMoneyRate"
          />
        </div>
      </div>
    </div>

    <SummaryModal class="w-[500px]" />
    <SubjectModal type="" class="w-[800px]" />
  </div>
</template>
<style lang="scss">
  .add-voucher-tablebox {
    position: relative;
    width: 97%;
    height: auto;

    /* width: 100%; */
    margin: 0 auto;
    font-size: 0;

    // 响应式设计优化
    @media (max-width: 768px) {
      .headul {
        height: 36px;
        line-height: 36px;

        .li2 ul {
          height: 18px;
          line-height: 18px;
        }

        .moneyul li {
          font-size: 10px;
        }
      }

      .bodyul {
        li {
          align-items: stretch; /* 确保所有子元素高度一致 */
          min-height: 42px;
          font-size: 12px;

          /* 移除固定的line-height，让内容自然撑开 */
        }

        .li-abstract .d1 {
          max-height: 60px; /* 移动端适当减小最大高度 */
          padding: 2px 4px;
          overflow: hidden auto; /* 保持滚动功能 */
          line-height: 1.3;
        }

        .connect,
        .connect-remove {
          height: 42px;
        }

        .add,
        .remove {
          top: 13px;
          width: 14px;
          height: 14px;
          font-size: 10px;
          line-height: 8px;
        }
      }

      .footerul {
        li {
          height: 42px;
          line-height: 42px;
        }

        .li3 div {
          margin-left: 10px;
        }
      }
    }

    @media (max-width: 480px) {
      .headul {
        height: 32px;
        line-height: 32px;

        .li2 ul {
          height: 16px;
          line-height: 16px;
        }

        .moneyul li {
          font-size: 9px;
        }
      }

      .bodyul {
        li {
          display: flex;
          align-items: stretch; /* 确保所有子元素高度一致 */
          min-height: 38px;
          font-size: 11px;

          /* 移除固定的line-height，让内容自然撑开 */
        }

        .li-abstract .d1 {
          max-height: 50px; /* 小屏幕进一步减小最大高度 */
          padding: 2px 4px;
          overflow: hidden auto; /* 保持滚动功能 */
          font-size: 10px; /* 小屏幕字体稍小 */
          line-height: 1.2;
        }

        .connect,
        .connect-remove {
          height: 38px;
        }

        .add,
        .remove {
          top: 11px;
          width: 12px;
          height: 12px;
          font-size: 9px;
          line-height: 6px;
        }
      }

      .footerul {
        li {
          height: 38px;
          line-height: 38px;
        }

        .li3 div {
          margin-left: 8px;
        }
      }
    }

    & > div {
      border: solid 1px rgb(217 218 220);
      border-right: none;
      border-bottom: none;
    }

    .selectbox {
      position: fixed; /* 改为 fixed 定位，相对于视口 */
      z-index: 1000; /* 确保在最上层 */
      height: 300px;
      max-height: 300px; /* 最大高度限制 */

      /* 确保不会超出视口边界 */
      max-height: calc(100vh - 100px); /* 视口高度减去一些边距 */
      overflow: visible; /* 允许内容溢出 */
      background: white;
      border: 1px solid #d9d9d9;
      border-radius: 6px;

      /* 添加阴影和边框，确保可见性 */
      box-shadow: 0 4px 12px rgb(0 0 0 / 15%);
    }

    /* 科目样式处理 */
    .subjectcont {
      display: flex;
      align-items: center;

      .subjectbox {
        flex: 1;
        font-size: 12px;
        line-height: 20px;
        text-align: left;
      }

      .supplier {
        display: flex;
        flex-direction: row;
        width: 90%;

        & > div:nth-child(2) {
          flex: 1;
        }
      }
    }

    // 内联编辑样式
    .amount-cell {
      position: relative;
      display: flex;
      align-items: center; /* 垂直居中对齐 */

      .amount-display {
        display: flex;
        align-items: center; /* 确保内容垂直居中 */
        justify-content: center; /* 确保内容水平居中 */
        width: 100%;
        height: 100%;
        cursor: pointer;
        transition: background-color 0.2s ease;

        &:hover {
          background-color: #f5f5f5;
        }

        /* 确保内部的 Ulvalue 组件也垂直居中 */
        :deep(.moneyul) {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100%;
          height: 100%;
          padding: 0;
          margin: 0;

          li {
            /* li 内部是纯文本，使用 line-height 和 text-align 即可 */
            flex: 1;
            line-height: 100%; /* 让文本垂直居中 */
            text-align: center;
          }
        }
      }

      .inline-amount-input {
        width: 100% !important;
        height: 100% !important;
        font-size: 13px;
        text-align: right;
        border: 2px solid #1890ff !important;
        border-radius: 4px;

        &:focus {
          box-shadow: 0 0 0 2px rgb(24 144 255 / 20%);
        }

        // 移除 ant-design 默认样式
        :deep(.ant-input) {
          text-align: right;
          border: none;
          box-shadow: none;
        }
      }
    }

    li {
      display: inline-block;
      vertical-align: top;
      border-right: solid 1px rgb(217 218 220);
      border-bottom: solid 1px rgb(217 218 220);
    }

    ul {
      display: flex;

      /* flex-direction: row; */

      li {
        flex: 1;
        font-size: 14px;
        color: rgb(51 51 51);
      }
    }

    .li2 {
      flex: 0.7;
      border-right: none;
      border-bottom: none;
    }

    /* 表头中的li2保持垂直布局 */
    .headul .li2 {
      display: flex;
      flex-direction: column; /* 垂直排列 tit 和 moneyul */
      justify-content: center;
    }

    /* 表体中的li2使用水平居中对齐 */
    .bodyul .li2 {
      display: flex;
      align-items: center; /* 垂直居中对齐 */
    }

    .headul {
      height: 40px;
      font-weight: 500;
      line-height: 40px;
      text-align: center;
      background-color: rgb(245 245 245);

      .li2 ul {
        height: 20px;
        line-height: 20px;
      }

      .moneyul li {
        font-size: 11px;
      }
    }

    .li-abstract {
      flex: 0.5;

      .d1 {
        width: 100%;
        max-height: 80px;
        padding: 2px 4px;
        overflow: hidden auto;
        line-height: 1.4;
        word-break: break-word;
        white-space: normal;

        &::-webkit-scrollbar {
          width: 4px;
        }

        &::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 2px;
        }

        &::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 2px;

          &:hover {
            background: #a8a8a8;
          }
        }
      }
    }

    .li-subject {
      flex: 1.5;
    }

    .li-currency {
      display: flex;
      flex: 0.5;
      align-items: center; /* 垂直居中对齐 */

      .currencybox {
        display: inline-block;
        width: 85%;
        margin-left: 10px;
        font-size: 12px;
        line-height: 22px;
        color: #999;
        vertical-align: middle;

        div {
          pointer-events: none;
        }
      }
    }

    .bodyul {
      position: relative;

      .connect,
      .connect-remove {
        position: absolute;
        top: 0;
        left: -20px;
        width: 20px;
        height: 48px;
      }

      .connect-remove {
        right: -20px;
        left: auto;
      }

      &:hover {
        .connect,
        .connect-remove {
          display: block;
        }

        .add,
        .remove {
          display: block;
          cursor: pointer;
        }
      }

      .add,
      .remove {
        position: absolute;
        top: 15px;
        left: -20px;
        z-index: 1;
        display: none;
        width: 16px;
        height: 16px;
        font-size: 11px;
        line-height: 10px;
        text-align: center;
        border-style: solid;
        border-width: 2px;
        border-radius: 50%;
      }

      .add {
        color: rgb(22 119 255 / 100%);
        border-color: rgb(22 119 255 / 100%);
      }

      .remove {
        right: -20px;
        left: auto;
        color: rgb(245 108 108 / 100%);
        border-color: rgb(245 108 108 / 100%);
      }

      li {
        display: flex;
        align-items: stretch; /* 确保所有子元素高度一致 */
        min-height: 48px;
        font-size: 13px;

        /* 移除固定的line-height，让内容自然撑开 */
      }
    }

    .footerul {
      background-color: rgb(254 251 242);

      li {
        height: 48px;
        line-height: 48px;
      }

      .li3 {
        flex: 2;
        text-align: left;

        div {
          margin-left: 15px;
        }
      }

      .li3-currency {
        flex: 2.51;
      }
    }

    .moneyul {
      display: flex;
      width: 100%;
      height: 100%;
      font-weight: 500;

      li {
        flex: 1;
        text-align: center;
      }

      li:nth-child(3) {
        border-right: solid 1px rgb(170 197 234);
      }

      li:nth-child(6) {
        border-right: solid 1px rgb(170 197 234);
      }

      li:nth-child(9) {
        border-right: solid 1px rgb(229 168 165);
      }
    }

    .exchange-input {
      position: absolute;
      z-index: 1;
      width: 160px;
      height: 100px;
      padding: 10px;
      font-size: 12px;
      background-color: #fff;
      border: solid 1px #ccc;
      border-radius: 5px;
    }
  }

  // 负数金额样式 - 提高优先级
  .add-voucher-tablebox ul.negative-amount li {
    font-weight: 500;
    color: #ff4d4f !important;
  }

  :deep(.negative-amount) {
    font-weight: 500;
    color: #ff4d4f !important;
  }

  :deep(.negative-amount li) {
    font-weight: 500;
    color: #ff4d4f !important;
  }
</style>
